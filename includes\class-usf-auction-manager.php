<?php

/**
 * Auction management functionality
 *
 * Handles auction lot operations and status management
 */

class USF_Auction_Manager {

    /**
     * Get active auctions with filters
     */
    public static function get_active_auctions($args = array()) {
        $defaults = array(
            'status' => 'active',
            'limit' => 12,
            'offset' => 0,
            'orderby' => 'closing_time',
            'order' => 'ASC'
        );

        $args = wp_parse_args($args, $defaults);
        
        // Only show auctions that haven't closed yet
        $args['closing_time_after'] = current_time('mysql');
        
        return USF_Database::get_lots($args);
    }

    /**
     * Get auction details with items
     */
    public static function get_auction_details($lot_id) {
        $lot = USF_Database::get_lot($lot_id);
        if (!$lot) {
            return null;
        }

        $items = USF_Database::get_lot_items($lot_id);
        $bid_count = USF_Database::get_lot_bid_count($lot_id);

        return array(
            'lot' => $lot,
            'items' => $items,
            'bid_count' => $bid_count,
            'is_active' => self::is_auction_active($lot),
            'time_remaining' => self::get_time_remaining($lot->closing_time)
        );
    }

    /**
     * Check if auction is still active
     */
    public static function is_auction_active($lot) {
        if (!$lot || $lot->status !== 'active') {
            return false;
        }

        $current_time = current_time('timestamp');
        $closing_time = strtotime($lot->closing_time);

        return $closing_time > $current_time;
    }

    /**
     * Get time remaining for auction
     */
    public static function get_time_remaining($closing_time) {
        $current_time = current_time('timestamp');
        $closing_timestamp = strtotime($closing_time);
        
        $time_diff = $closing_timestamp - $current_time;
        
        if ($time_diff <= 0) {
            return array(
                'expired' => true,
                'days' => 0,
                'hours' => 0,
                'minutes' => 0,
                'seconds' => 0
            );
        }

        $days = floor($time_diff / (24 * 60 * 60));
        $hours = floor(($time_diff % (24 * 60 * 60)) / (60 * 60));
        $minutes = floor(($time_diff % (60 * 60)) / 60);
        $seconds = $time_diff % 60;

        return array(
            'expired' => false,
            'days' => $days,
            'hours' => $hours,
            'minutes' => $minutes,
            'seconds' => $seconds,
            'total_seconds' => $time_diff
        );
    }

    /**
     * Close expired auctions
     */
    public static function close_expired_auctions() {
        global $wpdb;

        $table_lots = $wpdb->prefix . 'auction_lots';
        $current_time = current_time('mysql');

        // Update only auctions that are active AND have passed their closing time
        $sql = "UPDATE $table_lots SET status = 'closed' WHERE status = 'active' AND closing_time <= %s";
        $result = $wpdb->query($wpdb->prepare($sql, $current_time));

        return $result;
    }

    /**
     * Update auction status
     */
    public static function update_auction_status($lot_id, $status) {
        global $wpdb;

        $table_lots = $wpdb->prefix . 'auction_lots';

        $valid_statuses = array('active', 'closed', 'cancelled');
        if (!in_array($status, $valid_statuses)) {
            return false;
        }

        return $wpdb->update(
            $table_lots,
            array('status' => $status),
            array('lot_id' => $lot_id),
            array('%s'),
            array('%s')
        );
    }

    /**
     * Delete auction and related data
     */
    public static function delete_auction($lot_id) {
        global $wpdb;

        $table_lots = $wpdb->prefix . 'auction_lots';
        $table_items = $wpdb->prefix . 'auction_items';
        $table_bids = $wpdb->prefix . 'auction_bids';

        // Start transaction
        $wpdb->query('START TRANSACTION');

        try {
            // Delete bids
            $wpdb->delete($table_bids, array('lot_id' => $lot_id), array('%s'));
            
            // Delete items
            $wpdb->delete($table_items, array('lot_id' => $lot_id), array('%s'));
            
            // Delete lot
            $result = $wpdb->delete($table_lots, array('lot_id' => $lot_id), array('%s'));

            if ($result === false) {
                throw new Exception('Failed to delete auction lot');
            }

            $wpdb->query('COMMIT');
            return true;

        } catch (Exception $e) {
            $wpdb->query('ROLLBACK');
            return false;
        }
    }

    /**
     * Get auction statistics
     */
    public static function get_auction_stats() {
        $auction_stats = USF_Statistics_Helper::get_auction_statistics();
        $bid_stats = USF_Statistics_Helper::get_bid_statistics();

        // Combine auction and bid statistics for backward compatibility
        $stats = array_merge($auction_stats['auctions'], $bid_stats['bids']);
        $stats = array_merge($stats, $bid_stats['revenue']);
        $stats['average_bid'] = $stats['average_accepted_bid']; // Alias for backward compatibility

        return $stats;
    }

    /**
     * Search auctions
     */
    public static function search_auctions($search_term, $filters = array()) {
        $args = array(
            'search' => $search_term,
            'status' => 'active'
        );

        if (isset($filters['auction_house']) && !empty($filters['auction_house'])) {
            $args['auction_house'] = $filters['auction_house'];
        }

        if (isset($filters['limit'])) {
            $args['limit'] = (int) $filters['limit'];
        }

        if (isset($filters['offset'])) {
            $args['offset'] = (int) $filters['offset'];
        }

        if (isset($filters['orderby'])) {
            $args['orderby'] = $filters['orderby'];
        }

        if (isset($filters['order'])) {
            $args['order'] = $filters['order'];
        }

        return USF_Database::get_lots($args);
    }

    /**
     * Get auctions closing soon
     */
    public static function get_closing_soon($hours = 24) {
        global $wpdb;

        $table_lots = $wpdb->prefix . 'auction_lots';
        $current_time = current_time('mysql');
        $future_time = date('Y-m-d H:i:s', current_time('timestamp') + ($hours * 3600));

        $sql = "SELECT * FROM $table_lots 
                WHERE status = 'active' 
                AND closing_time > %s 
                AND closing_time <= %s 
                ORDER BY closing_time ASC";

        return $wpdb->get_results($wpdb->prepare($sql, $current_time, $future_time));
    }

    /**
     * Get popular auctions (most bids)
     */
    public static function get_popular_auctions($limit = 10) {
        global $wpdb;

        $table_lots = $wpdb->prefix . 'auction_lots';
        $table_bids = $wpdb->prefix . 'auction_bids';

        $sql = "SELECT l.*, COUNT(b.id) as bid_count 
                FROM $table_lots l 
                LEFT JOIN $table_bids b ON l.lot_id = b.lot_id 
                WHERE l.status = 'active' 
                GROUP BY l.id 
                ORDER BY bid_count DESC, l.closing_time ASC 
                LIMIT %d";

        return $wpdb->get_results($wpdb->prepare($sql, $limit));
    }

    /**
     * Validate auction data
     */
    public static function validate_auction_data($data) {
        $errors = array();

        // Required fields
        $required_fields = array('lot_id', 'auction_house', 'model', 'closing_time', 'min_offer');
        
        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                $errors[] = "Field '$field' is required";
            }
        }

        // Validate closing time
        if (!empty($data['closing_time'])) {
            $timestamp = strtotime($data['closing_time']);
            if ($timestamp === false) {
                $errors[] = "Invalid closing time format";
            } elseif ($timestamp <= current_time('timestamp')) {
                $errors[] = "Closing time must be in the future";
            }
        }

        // Validate minimum offer
        if (!empty($data['min_offer'])) {
            if (!is_numeric($data['min_offer']) || $data['min_offer'] < 0) {
                $errors[] = "Minimum offer must be a positive number";
            }
        }

        // Validate total units
        if (!empty($data['total_units'])) {
            if (!is_numeric($data['total_units']) || $data['total_units'] < 1) {
                $errors[] = "Total units must be a positive integer";
            }
        }

        return $errors;
    }
}
