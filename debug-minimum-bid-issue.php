<?php
/**
 * Debug script for the $84,899.50 minimum bid issue
 * 
 * This script helps identify why a user with a $90,000 rejected bid
 * is seeing a minimum of $84,899.50 instead of $90,000.01
 */

// Include WordPress
require_once('wp-config.php');

// Check if user is admin
if (!current_user_can('administrator')) {
    die('Access denied. Admin privileges required.');
}

echo "<h1>Debug: Minimum Bid Calculation Issue</h1>";
echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin: 20px 0;'>";
echo "<h2>Issue Summary</h2>";
echo "<p><strong>Problem:</strong> User with rejected bid of $90,000.00 seeing minimum of $84,899.50</p>";
echo "<p><strong>Expected:</strong> Minimum should be $90,000.01</p>";
echo "</div>";

// Get parameters from URL or use defaults
$lot_id = isset($_GET['lot_id']) ? sanitize_text_field($_GET['lot_id']) : 'ENTER_LOT_ID';
$user_id = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;

echo "<h2>Step 1: Input Parameters</h2>";
echo "<p>Testing with:</p>";
echo "<ul>";
echo "<li><strong>Lot ID:</strong> $lot_id</li>";
echo "<li><strong>User ID:</strong> $user_id</li>";
echo "</ul>";

if ($lot_id === 'ENTER_LOT_ID' || $user_id === 0) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; margin: 20px 0;'>";
    echo "<h3>Instructions</h3>";
    echo "<p>To use this debug script, add the lot_id and user_id parameters to the URL:</p>";
    echo "<p><code>debug-minimum-bid-issue.php?lot_id=YOUR_LOT_ID&user_id=USER_ID</code></p>";
    echo "<p>Replace YOUR_LOT_ID and USER_ID with the actual values from the problematic auction.</p>";
    echo "</div>";
    exit;
}

echo "<h2>Step 2: Database Queries</h2>";

// Test 1: Get auction details
echo "<h3>2.1 Auction Details</h3>";
$auction = USF_Database::get_lot($lot_id);
if ($auction) {
    echo "<p>✅ Auction found</p>";
    echo "<ul>";
    echo "<li><strong>Starting Price (min_offer):</strong> $" . number_format($auction->min_offer, 2) . "</li>";
    echo "<li><strong>Title:</strong> " . esc_html($auction->title) . "</li>";
    echo "</ul>";
} else {
    echo "<p>❌ Auction not found with lot_id: $lot_id</p>";
    exit;
}

// Test 2: Get user bid status
echo "<h3>2.2 User Bid Status</h3>";
$user_bid_status = USF_Database::get_user_bid_status($lot_id, $user_id);
if ($user_bid_status) {
    echo "<p>✅ User bid status found</p>";
    echo "<ul>";
    echo "<li><strong>Status:</strong> \"" . $user_bid_status->status . "\"</li>";
    echo "<li><strong>Bid Amount:</strong> $" . number_format($user_bid_status->bid_amount, 2) . "</li>";
    echo "<li><strong>Bid Time:</strong> " . $user_bid_status->bid_time . "</li>";
    echo "</ul>";
    
    // Check if status is exactly 'rejected'
    if ($user_bid_status->status === 'rejected') {
        echo "<p>✅ Status is correctly identified as 'rejected'</p>";
    } else {
        echo "<p>⚠️ Status is NOT 'rejected' - this might be the issue!</p>";
        echo "<p>Actual status: \"" . $user_bid_status->status . "\" (length: " . strlen($user_bid_status->status) . ")</p>";
    }
} else {
    echo "<p>❌ No bid status found for user $user_id on lot $lot_id</p>";
}

// Test 3: Get pending bid
echo "<h3>2.3 User Pending Bid</h3>";
$user_pending_bid = USF_Database::get_user_pending_bid_amount($lot_id, $user_id);
if ($user_pending_bid) {
    echo "<p>⚠️ User has pending bid: $" . number_format($user_pending_bid, 2) . "</p>";
    echo "<p>This would override rejected bid logic!</p>";
} else {
    echo "<p>✅ No pending bid found</p>";
}

// Test 4: Get highest rejected bid
echo "<h3>2.4 Highest Rejected Bid</h3>";
$user_rejected_bid = USF_Database::get_user_highest_rejected_bid_amount($lot_id, $user_id);
if ($user_rejected_bid) {
    echo "<p>✅ Highest rejected bid found: $" . number_format($user_rejected_bid, 2) . "</p>";
} else {
    echo "<p>❌ No rejected bid found - this is the problem!</p>";
}

echo "<h2>Step 3: Minimum Calculation Simulation</h2>";

// Simulate the exact logic from bid-form.php
$minimum_bid_amount = $auction->min_offer;
$minimum_bid_reason = 'starting price';

echo "<p><strong>Initial minimum:</strong> $" . number_format($minimum_bid_amount, 2) . " (starting price)</p>";

if ($user_pending_bid) {
    $minimum_bid_amount = $user_pending_bid + 0.01;
    $minimum_bid_reason = 'current bid';
    echo "<p><strong>Adjusted for pending bid:</strong> $" . number_format($minimum_bid_amount, 2) . " (pending bid + 0.01)</p>";
} else {
    if ($user_rejected_bid) {
        $minimum_bid_amount = max($auction->min_offer, $user_rejected_bid + 0.01);
        $minimum_bid_reason = 'rejected bid';
        echo "<p><strong>Adjusted for rejected bid:</strong> max($" . number_format($auction->min_offer, 2) . ", $" . number_format($user_rejected_bid + 0.01, 2) . ") = $" . number_format($minimum_bid_amount, 2) . "</p>";
    }
}

echo "<h2>Step 4: Analysis</h2>";

if (abs($minimum_bid_amount - 84899.50) < 0.01) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb;'>";
    echo "<h3>🔍 ISSUE FOUND!</h3>";
    echo "<p>The calculated minimum ($" . number_format($minimum_bid_amount, 2) . ") matches the problematic $84,899.50!</p>";
    echo "<p><strong>Root cause:</strong> " . $minimum_bid_reason . "</p>";
    echo "</div>";
} else {
    echo "<div style='background: #d1ecf1; padding: 15px; border: 1px solid #bee5eb;'>";
    echo "<h3>🤔 Calculation Mismatch</h3>";
    echo "<p>Our calculated minimum ($" . number_format($minimum_bid_amount, 2) . ") does NOT match the problematic $84,899.50</p>";
    echo "<p>This suggests the issue might be:</p>";
    echo "<ul>";
    echo "<li>Cached data or old page version</li>";
    echo "<li>Different calculation logic elsewhere</li>";
    echo "<li>JavaScript override</li>";
    echo "<li>Different lot_id or user_id than expected</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<h2>Step 5: Recommendations</h2>";
echo "<ol>";
echo "<li><strong>Clear any caching</strong> (page cache, object cache, etc.)</li>";
echo "<li><strong>Check browser cache</strong> - try hard refresh (Ctrl+F5)</li>";
echo "<li><strong>Verify lot_id and user_id</strong> are correct for the problematic case</li>";
echo "<li><strong>Check database directly</strong> for bid records</li>";
echo "<li><strong>Add debug parameter</strong> to the auction page: <code>?debug=1</code></li>";
echo "</ol>";

echo "<h2>Step 6: Database Query for Manual Verification</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6;'>";
echo "<h4>Run this SQL query to check bid records:</h4>";
echo "<pre>";
echo "SELECT * FROM wp_auction_bids \n";
echo "WHERE lot_id = '$lot_id' AND user_id = $user_id \n";
echo "ORDER BY bid_time DESC;";
echo "</pre>";
echo "</div>";
?>
