<?php

/**
 * CSV Parser for multiple auction house formats
 *
 * Handles parsing of CSV files from AT&T, Bell, and T-Mobile auction houses
 */

class USF_CSV_Parser {

    /**
     * Parse CSV file and return structured data
     */
    public static function parse_csv($file_path, $auction_house = null) {
        if (!file_exists($file_path)) {
            return array('error' => 'File not found');
        }

        $file_content = file_get_contents($file_path);
        if ($file_content === false) {
            return array('error' => 'Could not read file');
        }

        // Use manual auction house selection if provided, otherwise auto-detect
        if ($auction_house) {
            // Convert short codes to full names
            $auction_house_map = array(
                'att' => 'AT&T',
                'bell' => 'Bell',
                'tmobile' => 'T-Mobile'
            );
            
            if (isset($auction_house_map[$auction_house])) {
                $auction_house = $auction_house_map[$auction_house];
            } else {
                return array('error' => 'Invalid auction house specified');
            }
        } else {
            // Fallback to auto-detection
            $auction_house = self::detect_auction_house($file_content);
            if (!$auction_house) {
                return array('error' => 'Could not detect auction house format');
            }
        }

        // Parse based on auction house format
        switch ($auction_house) {
            case 'AT&T':
                return self::parse_att_format($file_content);
            case 'Bell':
                return self::parse_bell_format($file_content);
            case 'T-Mobile':
                return self::parse_tmobile_format($file_content);
            default:
                return array('error' => 'Unsupported auction house format');
        }
    }

    /**
     * Detect auction house based on file content
     */
    private static function detect_auction_house($content) {
        // Check for URL patterns or specific column headers
        if (strpos($content, 'bstock.com/mobilecarrier') !== false) {
            return 'AT&T';
        } elseif (strpos($content, 'bstock.com/wirelessnetwork') !== false) {
            return 'Bell';
        } elseif (strpos($content, 'bstock.com/superior') !== false) {
            return 'T-Mobile';
        }

        // Check for specific column patterns
        $lines = explode("\n", $content);
        if (count($lines) > 0) {
            $first_line = $lines[0];
            
            // AT&T specific columns
            if (strpos($first_line, 'Part #') !== false && strpos($first_line, 'OEM') !== false) {
                return 'AT&T';
            }
            
            // Bell specific columns
            if (strpos($first_line, 'Functionality') !== false && strpos($first_line, 'Manufacturer') !== false) {
                return 'Bell';
            }
            
            // T-Mobile specific columns
            if (strpos($first_line, 'Warehouse #') !== false && strpos($first_line, 'Data Clear') !== false) {
                return 'T-Mobile';
            }
        }

        return false;
    }

    /**
     * Generic CSV parsing method to reduce code duplication
     */
    private static function parse_generic_format($content, $auction_house, $items_header, $lot_header_parser, $item_line_parser) {
        $lines = array_map('str_getcsv', explode("\n", $content));
        $lines = array_filter($lines, function($line) {
            return !empty($line) && count($line) > 1;
        });

        $lots = array();
        $current_lot = null;
        $in_items_section = false;

        foreach ($lines as $line_num => $line) {
            // Skip empty lines
            if (empty($line) || count($line) < 2) {
                continue;
            }

            // Check if this is a lot header line
            if (isset($line[0]) && strpos($line[0], 'https://') === 0) {
                // This is a lot header
                $in_items_section = false;
                $current_lot = self::$lot_header_parser($line);
                if ($current_lot) {
                    $current_lot['items'] = array();
                    $lots[] = $current_lot;
                }
            } elseif (isset($line[0]) && $line[0] === $items_header) {
                // This is the items header
                $in_items_section = true;
            } elseif ($in_items_section && $current_lot && !empty($line[0])) {
                // This is an item line
                $item = self::$item_line_parser($line);
                if ($item) {
                    $lots[count($lots) - 1]['items'][] = $item;
                }
            }
        }

        return array(
            'auction_house' => $auction_house,
            'lots' => $lots,
            'total_lots' => count($lots)
        );
    }

    /**
     * Parse AT&T CSV format
     */
    private static function parse_att_format($content) {
        return self::parse_generic_format($content, 'AT&T', 'Lot #', 'parse_att_lot_header', 'parse_att_item_line');
    }

    /**
     * Parse AT&T lot header
     */
    private static function parse_att_lot_header($line) {
        // Expected format: URL,Lot ID,Model,Memory,Grade,Units,Closing Time,Min Offer,Your Bid
        if (count($line) < 8) {
            return null;
        }

        return array(
            'url' => $line[0],
            'lot_id' => $line[1],
            'model' => $line[2],
            'memory' => $line[3],
            'grade' => $line[4],
            'total_units' => intval($line[5]),
            'closing_time' => self::parse_datetime($line[6]),
            'min_offer' => self::parse_currency($line[7])
        );
    }

    /**
     * Parse AT&T item line
     */
    private static function parse_att_item_line($line) {
        // Expected format: Lot #,Qty,OEM,Model,Item Description,Part #,Category,Grade,Package Type,Capacity,Color
        if (count($line) < 11) {
            return null;
        }

        return array(
            'quantity' => intval($line[1]),
            'manufacturer' => $line[2],
            'model' => $line[3],
            'description' => $line[4],
            'part_number' => $line[5],
            'category' => $line[6],
            'grade' => $line[7],
            'capacity' => $line[9],
            'color' => $line[10]
        );
    }

    /**
     * Parse Bell CSV format
     */
    private static function parse_bell_format($content) {
        return self::parse_generic_format($content, 'Bell', 'Manufacturer', 'parse_bell_lot_header', 'parse_bell_item_line');
    }

    /**
     * Parse Bell lot header
     */
    private static function parse_bell_lot_header($line) {
        // Expected format: URL,Lot ID,Model,Memory,Grade,Units,Closing Time,Min Offer,Your Bid
        if (count($line) < 8) {
            return null;
        }

        return array(
            'url' => $line[0],
            'lot_id' => $line[1],
            'model' => $line[2],
            'memory' => $line[3],
            'grade' => $line[4],
            'total_units' => intval($line[5]),
            'closing_time' => self::parse_datetime($line[6]),
            'min_offer' => self::parse_currency($line[7])
        );
    }

    /**
     * Parse Bell item line
     */
    private static function parse_bell_item_line($line) {
        // Expected format: Manufacturer,Model,Capacity,Item Description,Carrier,Functionality,Grade,Qty
        if (count($line) < 8) {
            return null;
        }

        return array(
            'manufacturer' => $line[0],
            'model' => $line[1],
            'capacity' => $line[2],
            'description' => $line[3],
            'carrier' => $line[4],
            'grade' => $line[6],
            'quantity' => intval($line[7])
        );
    }

    /**
     * Parse T-Mobile CSV format
     */
    private static function parse_tmobile_format($content) {
        return self::parse_generic_format($content, 'T-Mobile', 'Manufacturer', 'parse_tmobile_lot_header', 'parse_tmobile_item_line');
    }

    /**
     * Parse T-Mobile lot header
     */
    private static function parse_tmobile_lot_header($line) {
        // Expected format: URL,Lot ID,Model,Memory,Grade,Units,Closing Time,Min Offer,Your Bid
        if (count($line) < 8) {
            return null;
        }

        return array(
            'url' => $line[0],
            'lot_id' => $line[1],
            'model' => $line[2],
            'memory' => $line[3],
            'grade' => $line[4],
            'total_units' => intval($line[5]),
            'closing_time' => self::parse_datetime($line[6]),
            'min_offer' => self::parse_currency($line[7])
        );
    }

    /**
     * Parse T-Mobile item line
     */
    private static function parse_tmobile_item_line($line) {
        // Expected format: Manufacturer,Model,Grade,Qty,Item Description,Category,Warehouse #,Seller,Currency,Capacity,Carrier,Color,Data Clear,Accessory
        if (count($line) < 14) {
            return null;
        }

        return array(
            'manufacturer' => $line[0],
            'model' => $line[1],
            'grade' => $line[2],
            'quantity' => intval($line[3]),
            'description' => $line[4],
            'category' => $line[5],
            'capacity' => $line[9],
            'carrier' => $line[10],
            'color' => $line[11]
        );
    }

    /**
     * Parse datetime string to MySQL format
     */
    private static function parse_datetime($datetime_str) {
        // Handle various datetime formats
        $datetime_str = trim($datetime_str, '"');
        
        // Try to parse the datetime
        $timestamp = strtotime($datetime_str);
        if ($timestamp === false) {
            return null;
        }

        return date('Y-m-d H:i:s', $timestamp);
    }

    /**
     * Parse currency string to decimal
     */
    private static function parse_currency($currency_str) {
        // Remove currency symbols and commas
        $currency_str = preg_replace('/[^\d.]/', '', $currency_str);
        return floatval($currency_str);
    }

    /**
     * Import parsed data to database
     */
    public static function import_to_database($parsed_data, $source_file) {
        if (!isset($parsed_data['lots']) || empty($parsed_data['lots'])) {
            return array('error' => 'No lots found in parsed data');
        }

        $imported_count = 0;
        $errors = array();

        foreach ($parsed_data['lots'] as $lot_data) {
            try {
                // Prepare lot data for database
                $db_lot_data = array(
                    'lot_id' => $lot_data['lot_id'],
                    'source_file' => $source_file,
                    'auction_house' => $parsed_data['auction_house'],
                    'url' => $lot_data['url'],
                    'model' => $lot_data['model'],
                    'memory' => $lot_data['memory'],
                    'grade' => $lot_data['grade'],
                    'total_units' => $lot_data['total_units'],
                    'closing_time' => $lot_data['closing_time'],
                    'min_offer' => $lot_data['min_offer'],
                    'status' => 'active'
                );

                // Save lot to database
                $result = USF_Database::save_lot($db_lot_data);
                
                if ($result !== false) {
                    // Save lot items
                    if (isset($lot_data['items']) && !empty($lot_data['items'])) {
                        USF_Database::save_lot_items($lot_data['lot_id'], $lot_data['items']);
                    }
                    $imported_count++;
                } else {
                    $errors[] = "Failed to import lot: " . $lot_data['lot_id'];
                }

            } catch (Exception $e) {
                $errors[] = "Error importing lot " . $lot_data['lot_id'] . ": " . $e->getMessage();
            }
        }

        return array(
            'success' => true,
            'imported_count' => $imported_count,
            'total_lots' => count($parsed_data['lots']),
            'errors' => $errors
        );
    }
}
