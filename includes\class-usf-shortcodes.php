<?php

/**
 * Shortcode functionality
 *
 * Handles [allauctions] and [singleauction] shortcodes
 */

class USF_Shortcodes {

    /**
     * Initialize shortcodes
     */
    public static function init() {
        add_shortcode('allauctions', array(self::class, 'render_all_auctions'));
        add_shortcode('singleauction', array(self::class, 'render_single_auction'));
    }

    /**
     * Render all auctions shortcode
     */
    public static function render_all_auctions($atts) {
        $atts = shortcode_atts(array(
            'limit' => 12,
            'orderby' => 'closing_time',
            'order' => 'ASC',
            'auction_house' => '',
            'show_search' => 'true',
            'show_filters' => 'true'
        ), $atts);

        // Get current page for pagination
        $paged = get_query_var('paged') ? get_query_var('paged') : 1;
        $offset = ($paged - 1) * $atts['limit'];

        // Get search and filter parameters
        $search = isset($_GET['auction_search']) ? sanitize_text_field($_GET['auction_search']) : '';
        $filter_house = isset($_GET['auction_house']) ? sanitize_text_field($_GET['auction_house']) : $atts['auction_house'];

        // Build query arguments
        $args = array(
            'status' => 'active',
            'limit' => intval($atts['limit']),
            'offset' => $offset,
            'orderby' => $atts['orderby'],
            'order' => $atts['order'],
            'search' => $search,
            'auction_house' => $filter_house
        );

        // Get auctions and total count
        $auctions = USF_Database::get_lots($args);
        $total_auctions = USF_Database::get_lots_count($args);

        // Start output buffering
        ob_start();

        // Enqueue styles and scripts
        wp_enqueue_style('usf-auction-public');
        wp_enqueue_script('usf-auction-public');

        echo '<div class="usf-auctions-container">';

        // Render search and filters
        if ($atts['show_search'] === 'true' || $atts['show_filters'] === 'true') {
            self::render_search_filters($atts, $search, $filter_house);
        }

        // Render auctions grid
        if (!empty($auctions)) {
            echo '<div class="usf-auctions-grid">';
            foreach ($auctions as $auction) {
                self::render_auction_card($auction);
            }
            echo '</div>';

            // Render pagination
            self::render_pagination($paged, $total_auctions, $atts['limit']);
        } else {
            echo '<div class="usf-no-auctions">';
            echo '<p>No active auctions found.</p>';
            echo '</div>';
        }

        echo '</div>';

        return ob_get_clean();
    }

    /**
     * Render single auction shortcode
     */
    public static function render_single_auction($atts) {
        $atts = shortcode_atts(array(
            'lot_id' => '',
            'show_bid_form' => 'true',
            'show_items' => 'true'
        ), $atts);

        // Get lot_id from URL parameter if not provided
        if (empty($atts['lot_id'])) {
            $atts['lot_id'] = get_query_var('lot_id');
        }

        if (empty($atts['lot_id'])) {
            return '<p>No auction lot specified.</p>';
        }

        // Get auction details
        $auction_details = USF_Auction_Manager::get_auction_details($atts['lot_id']);
        if (!$auction_details) {
            return '<p>Auction lot not found.</p>';
        }

        // Start output buffering
        ob_start();

        // Enqueue styles and scripts
        wp_enqueue_style('usf-auction-public');
        wp_enqueue_script('usf-auction-public');

        echo '<div class="usf-single-auction">';

        // Render auction details
        self::render_auction_details($auction_details, $atts);

        echo '</div>';

        return ob_get_clean();
    }

    /**
     * Render search and filters
     */
    private static function render_search_filters($atts, $search, $filter_house) {
        echo '<div class="usf-auction-filters">';
        echo '<form method="get" class="usf-filter-form">';

        if ($atts['show_search'] === 'true') {
            echo '<div class="usf-search-field">';
            echo '<input type="text" name="auction_search" placeholder="Search auctions..." value="' . esc_attr($search) . '">';
            echo '<button type="submit">Search</button>';
            echo '</div>';
        }

        if ($atts['show_filters'] === 'true') {
            echo '<div class="usf-filter-fields">';
            
            // Auction house filter
            echo '<select name="auction_house">';
            echo '<option value="">All Auction Houses</option>';
            $auction_houses = array('AT&T', 'Bell', 'T-Mobile');
            foreach ($auction_houses as $house) {
                $selected = ($filter_house === $house) ? 'selected' : '';
                echo '<option value="' . esc_attr($house) . '" ' . $selected . '>' . esc_html($house) . '</option>';
            }
            echo '</select>';

            echo '<button type="submit">Filter</button>';
            echo '</div>';
        }

        echo '</form>';
        echo '</div>';
    }

    /**
     * Render auction card
     */
    private static function render_auction_card($auction) {
        $time_remaining = USF_Auction_Manager::get_time_remaining($auction->closing_time);
        $bid_count = USF_Database::get_lot_bid_count($auction->lot_id);
        $is_active = USF_Auction_Manager::is_auction_active($auction);

        // Get user bid status if logged in
        $user_bid_status = null;
        if (is_user_logged_in()) {
            $user_bid_status = USF_Database::get_user_bid_status($auction->lot_id, get_current_user_id());
        }

        echo '<div class="usf-auction-card" data-lot-id="' . esc_attr($auction->lot_id) . '">';
        
        echo '<div class="usf-auction-header">';
        echo '<h3 class="usf-auction-title">' . esc_html($auction->model) . '</h3>';
        echo '<span class="usf-auction-house">' . esc_html($auction->auction_house) . '</span>';
        echo '</div>';

        echo '<div class="usf-auction-details">';
        echo '<div class="usf-detail-row">';
        echo '<span class="usf-label">Lot ID:</span>';
        echo '<span class="usf-value">' . esc_html($auction->lot_id) . '</span>';
        echo '</div>';

        echo '<div class="usf-detail-row">';
        echo '<span class="usf-label">Memory:</span>';
        echo '<span class="usf-value">' . esc_html($auction->memory) . '</span>';
        echo '</div>';

        echo '<div class="usf-detail-row">';
        echo '<span class="usf-label">Grade:</span>';
        echo '<span class="usf-value">' . esc_html($auction->grade) . '</span>';
        echo '</div>';

        echo '<div class="usf-detail-row">';
        echo '<span class="usf-label">Units:</span>';
        echo '<span class="usf-value">' . esc_html($auction->total_units) . '</span>';
        echo '</div>';

        echo '<div class="usf-detail-row">';
        // Check if auction has any bids to determine label and price
        $has_bids = USF_Database::auction_has_bids($auction->lot_id);
        $display_price = USF_Database::get_auction_display_price($auction->lot_id, $auction->min_offer);
        $price_label = $has_bids ? 'Updated Price:' : 'Starting Price:';

        echo '<span class="usf-label">' . esc_html($price_label) . '</span>';
        echo '<span class="usf-value">$' . number_format($display_price, 2) . '</span>';
        echo '</div>';
        echo '</div>';

        // User Bid Status
        if ($user_bid_status) {
            echo '<div class="usf-user-bid-status">';
            if ($user_bid_status->status === 'pending') {
                echo '<div class="usf-bid-badge usf-bid-pending">';
                echo '<span class="usf-badge-icon">⏳</span>';
                echo '<span class="usf-badge-text">Bid Submitted ($' . number_format($user_bid_status->bid_amount, 2) . ')</span>';
                echo '</div>';
            } elseif ($user_bid_status->status === 'accepted') {
                echo '<div class="usf-bid-badge usf-bid-accepted">';
                echo '<span class="usf-badge-icon">🎉</span>';
                echo '<span class="usf-badge-text">Bid Accepted ($' . number_format($user_bid_status->bid_amount, 2) . ')</span>';
                echo '</div>';
            } elseif ($user_bid_status->status === 'rejected') {
                echo '<div class="usf-bid-badge usf-bid-rejected">';
                echo '<span class="usf-badge-icon">❌</span>';
                echo '<span class="usf-badge-text">Bid Rejected - Can Bid Again</span>';
                echo '</div>';
            }
            echo '</div>';
        }

        // Time remaining
        echo '<div class="usf-auction-timer">';
        if ($time_remaining['expired']) {
            echo '<span class="usf-timer-expired">Auction Closed</span>';
        } else {
            echo '<div class="usf-countdown" data-closing-time="' . esc_attr($auction->closing_time) . '">';
            echo '<span class="usf-timer-label">Time Remaining:</span>';
            echo '<div class="usf-timer-display">';
            echo '<span class="usf-days">' . $time_remaining['days'] . 'd</span> ';
            echo '<span class="usf-hours">' . $time_remaining['hours'] . 'h</span> ';
            echo '<span class="usf-minutes">' . $time_remaining['minutes'] . 'm</span> ';
            echo '<span class="usf-seconds">' . $time_remaining['seconds'] . 's</span>';
            echo '</div>';
            echo '</div>';
        }
        echo '</div>';

        // Action buttons
        echo '<div class="usf-auction-actions">';
        if ($is_active) {
            // Get the configured single auction page
            $single_auction_page_id = USF_Database::get_setting('single_auction_page', '');
            if (!empty($single_auction_page_id)) {
                $single_url = add_query_arg('lot_id', $auction->lot_id, get_permalink($single_auction_page_id));
            } else {
                // Fallback to current page if no single auction page is configured
                $single_url = add_query_arg('lot_id', $auction->lot_id, get_permalink());
            }
            echo '<a href="' . esc_url($single_url) . '" class="usf-btn usf-btn-primary">View Details</a>';
            echo '<button class="usf-btn usf-btn-secondary usf-quick-bid" data-lot-id="' . esc_attr($auction->lot_id) . '">Quick Bid</button>';
        } else {
            echo '<span class="usf-auction-closed">Auction Closed</span>';
        }
        echo '</div>';

        echo '</div>';
    }

    /**
     * Render auction details
     */
    private static function render_auction_details($auction_details, $atts) {
        $lot = $auction_details['lot'];
        $items = $auction_details['items'];
        $time_remaining = $auction_details['time_remaining'];
        $is_active = $auction_details['is_active'];

        // Hero Section
        echo '<div class="usf-auction-header">';
        echo '<h1>' . esc_html($lot->model);
        if (!empty($lot->memory)) {
            echo ' - ' . esc_html($lot->memory);
        }
        echo '</h1>';
        echo '<div class="usf-auction-meta">';
        echo '<span class="usf-lot-id">Lot #' . esc_html($lot->lot_id) . '</span>';
        echo '<span class="usf-auction-house">' . esc_html($lot->auction_house) . '</span>';
        if ($is_active) {
            echo '<span class="usf-status-badge usf-status-active">Active Auction</span>';
        } else {
            echo '<span class="usf-status-badge usf-status-closed">Auction Closed</span>';
        }
        echo '</div>';
        echo '</div>';

        // Time remaining section - moved below header
        if (!$time_remaining['expired']) {
            echo '<div class="usf-time-remaining">';
            echo '<h3>Time Remaining</h3>';
            echo '<div class="usf-countdown-large" data-closing-time="' . esc_attr($lot->closing_time) . '">';
            echo '<div class="usf-countdown-item"><span class="usf-number">' . $time_remaining['days'] . '</span><span class="usf-label">Days</span></div>';
            echo '<div class="usf-countdown-item"><span class="usf-number">' . $time_remaining['hours'] . '</span><span class="usf-label">Hours</span></div>';
            echo '<div class="usf-countdown-item"><span class="usf-number">' . $time_remaining['minutes'] . '</span><span class="usf-label">Minutes</span></div>';
            echo '<div class="usf-countdown-item"><span class="usf-number">' . $time_remaining['seconds'] . '</span><span class="usf-label">Seconds</span></div>';
            echo '</div>';
            echo '</div>';
        }

        echo '<div class="usf-auction-content">';
        
        // Left column - Details
        echo '<div class="usf-auction-main">';
        
        // Current Bid Information (New Section) - removed Total Bids
        echo '<div class="usf-current-bid-info">';
        echo '<h3>Current Bid Information</h3>';
        echo '<div class="usf-bid-stats">';
        
        // Get current highest bid
        $current_bid = USF_Database::get_highest_bid($lot->lot_id);
        $bid_count = USF_Database::get_lot_bid_count($lot->lot_id);
        
        echo '<div class="usf-bid-stat-card">';
        // Use dynamic price label logic like auction cards
        $has_bids = USF_Database::auction_has_bids($lot->lot_id);
        $display_price = USF_Database::get_auction_display_price($lot->lot_id, $lot->min_offer);
        $price_label = $has_bids ? 'Updated Price' : 'Starting Price';

        echo '<div class="usf-stat-label">' . esc_html($price_label) . '</div>';
        echo '<div class="usf-stat-value">$' . number_format($display_price, 2) . '</div>';
        echo '</div>';
        
        echo '<div class="usf-bid-stat-card">';
        echo '<div class="usf-stat-label">Avg. Cost Per Unit</div>';
        $avg_cost = $current_bid ? ($current_bid / $lot->total_units) : ($lot->min_offer / $lot->total_units);
        echo '<div class="usf-stat-value">$' . number_format($avg_cost, 2) . '</div>';
        echo '</div>';
        
        echo '<div class="usf-bid-stat-card">';
        echo '<div class="usf-stat-label">Closes In</div>';
        if ($time_remaining['expired']) {
            echo '<div class="usf-stat-value usf-expired">Closed</div>';
        } else {
            echo '<div class="usf-stat-value">' . $time_remaining['days'] . 'd ' . $time_remaining['hours'] . 'h</div>';
        }
        echo '</div>';
        
        echo '</div>';
        echo '</div>';
        
        echo '<div class="usf-auction-summary">';
        echo '<h3>Lot Details</h3>';
        echo '<table class="usf-summary-table">';
        echo '<tr><td>Grade:</td><td><span class="usf-grade-badge">' . esc_html($lot->grade) . '</span></td></tr>';
        echo '<tr><td>Total Units:</td><td><strong>' . esc_html($lot->total_units) . '</strong></td></tr>';
        echo '<tr><td>Minimum Offer:</td><td><strong>$' . number_format($lot->min_offer, 2) . '</strong></td></tr>';
        echo '<tr><td>Closing Time:</td><td>' . date('M j, Y \a\t g:i A T', strtotime($lot->closing_time)) . '</td></tr>';
        echo '<tr><td>Location:</td><td>' . esc_html($lot->location ?? 'Not specified') . '</td></tr>';
        echo '</table>';
        echo '</div>';

        echo '</div>';

        // Right column - Bid form and additional info
        echo '<div class="usf-auction-sidebar">';

        // Bid form
        if ($atts['show_bid_form'] === 'true' && $is_active) {
            self::render_bid_form($lot);
        } elseif (!$is_active) {
            echo '<div class="usf-bid-form-container">';
            echo '<h3>Auction Closed</h3>';
            echo '<p>This auction has ended and is no longer accepting bids.</p>';
            echo '<div class="usf-auction-actions">';
            echo '<a href="' . esc_url(remove_query_arg('lot_id')) . '" class="usf-btn usf-btn-primary">View Other Auctions</a>';
            echo '</div>';
            echo '</div>';
        }

        // Shipping & Additional Info
        echo '<div class="usf-shipping-info">';
        echo '<h3>Shipping Information</h3>';
        echo '<div class="usf-info-grid">';
        echo '<div class="usf-info-item">';
        echo '<span class="usf-info-label">Shipping Estimate:</span>';
        echo '<span class="usf-info-value">Buyer Arranges</span>';
        echo '</div>';
        echo '<div class="usf-info-item">';
        echo '<span class="usf-info-label">Payment Terms:</span>';
        echo '<span class="usf-info-value">Due within 48 hours</span>';
        echo '</div>';
        echo '</div>';
        echo '</div>';

        echo '</div>';

        echo '</div>';

        // Enhanced Items table
        if ($atts['show_items'] === 'true' && !empty($items)) {
            echo '<div class="usf-auction-items">';
            echo '<h3>Manifest</h3>';
            echo '<div class="usf-manifest-summary">';
            echo '<div class="usf-manifest-stats">';
            echo '<div class="usf-manifest-stat">';
            echo '<span class="usf-stat-number">' . array_sum(wp_list_pluck($items, 'quantity')) . '</span>';
            echo '<span class="usf-stat-label">Total Items</span>';
            echo '</div>';
            echo '<div class="usf-manifest-stat">';
            echo '<span class="usf-stat-number">' . count($items) . '</span>';
            echo '<span class="usf-stat-label">Unique SKUs</span>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            self::render_items_table($items);
            echo '</div>';
        }
    }

    /**
     * Render bid form
     */
    private static function render_bid_form($lot) {
        echo '<div class="usf-bid-form-container">';

        // Check if user is logged in
        if (!is_user_logged_in()) {
            echo '<h3>Login Required</h3>';
            echo '<p>You must be logged in to place a bid on this auction.</p>';
            echo '<div class="usf-login-actions">';
            echo '<a href="' . esc_url(wp_login_url(get_permalink())) . '" class="usf-btn usf-btn-primary">Login</a>';
            echo '<a href="' . esc_url(wp_registration_url()) . '" class="usf-btn usf-btn-secondary">Register</a>';
            echo '</div>';
            echo '</div>';
            return;
        }

        // Get current user data
        $current_user = wp_get_current_user();

        // Check user's bid status for this lot
        $user_bid_status = USF_Database::get_user_bid_status($lot->lot_id, $current_user->ID);
        $can_bid = USF_Database::user_can_bid($lot->lot_id, $current_user->ID);

        // Check if auction is still active
        $closing_time = strtotime($lot->closing_time);
        $now = current_time('timestamp');
        $is_auction_active = $closing_time > $now;


        if ($user_bid_status) {
            if ($user_bid_status->status === 'pending') {
                // User has pending bid
                echo '<h3>Active Bid</h3>';
                echo '<div class="usf-bid-status usf-bid-pending">';
                echo '<div class="usf-status-icon">⏳</div>';
                echo '<div class="usf-status-content">';
                echo '<p><strong>Your bid is under review</strong></p>';
                echo '<div class="usf-bid-details">';
                echo '<p><strong>Current Bid Amount:</strong> $' . number_format($user_bid_status->bid_amount, 2) . '</p>';
                echo '<p><strong>Submitted:</strong> ' . date('M j, Y g:i A', strtotime($user_bid_status->bid_time)) . '</p>';
                echo '</div>';
                echo '<p>You will receive an email notification once a decision has been made on your bid.</p>';
                echo '</div>';
                echo '</div>';

                if ($is_auction_active) {
                    // Allow user to place a higher bid
                    echo '<div class="usf-higher-bid-section">';
                    echo '<h4>Place a Higher Bid</h4>';
                    echo '<p>You can submit a higher bid amount to increase your chances of winning this auction.</p>';

                    echo '<form class="usf-bid-form" data-lot-id="' . esc_attr($lot->lot_id) . '" data-current-bid="' . esc_attr($user_bid_status->bid_amount) . '">';
                    echo wp_nonce_field('usf_submit_bid', 'usf_bid_nonce', true, false);

                    echo '<div class="usf-form-group">';
                    echo '<label for="bid_amount">New Bid Amount * (Must be higher than $' . number_format($user_bid_status->bid_amount, 2) . ')</label>';
                    echo '<input type="number" id="bid_amount" name="bid_amount" min="' . esc_attr($user_bid_status->bid_amount + 0.01) . '" step="0.01" placeholder="' . esc_attr($user_bid_status->bid_amount + 1) . '" required>';
                    echo '<small>Enter a bid amount higher than your current bid of $' . number_format($user_bid_status->bid_amount, 2) . '.</small>';
                    echo '</div>';

                    echo '<div class="usf-form-group">';
                    echo '<label class="usf-checkbox-label">';
                    echo '<input type="checkbox" id="terms_accepted" name="terms_accepted" required>';
                    echo 'I agree to the terms and conditions *';
                    echo '</label>';
                    echo '</div>';

                    echo '<div class="usf-form-actions">';
                    echo '<button type="submit" class="usf-btn usf-btn-primary usf-btn-large">Submit Higher Bid</button>';
                    echo '</div>';

                    echo '<div class="usf-bid-messages"></div>';
                    echo '</form>';
                    echo '</div>';
                }
            } elseif ($user_bid_status->status === 'accepted') {
                // User's bid was accepted
                echo '<h3>🎉 Congratulations!</h3>';
                echo '<div class="usf-bid-status usf-bid-accepted">';
                echo '<div class="usf-status-icon">🎉</div>';
                echo '<div class="usf-status-content">';
                echo '<p><strong>Your bid has been accepted!</strong></p>';
                echo '<div class="usf-bid-details">';
                echo '<p><strong>Winning Bid Amount:</strong> $' . number_format($user_bid_status->bid_amount, 2) . '</p>';
                echo '<p><strong>Submitted:</strong> ' . date('M j, Y g:i A', strtotime($user_bid_status->bid_time)) . '</p>';
                echo '</div>';
                echo '<p>Please check your email for payment instructions and next steps.</p>';
                echo '</div>';
                echo '</div>';
            } elseif ($user_bid_status->status === 'rejected') {
                // User's bid was rejected - allow new bid
                echo '<h3>Previous Bid Update</h3>';
                echo '<div class="usf-bid-status usf-bid-rejected">';
                echo '<div class="usf-status-icon">❌</div>';
                echo '<div class="usf-status-content">';
                echo '<p><strong>Your previous bid was not accepted</strong></p>';
                echo '<div class="usf-bid-details">';
                echo '<p><strong>Previous Bid Amount:</strong> $' . number_format($user_bid_status->bid_amount, 2) . '</p>';
                echo '<p><strong>Submitted:</strong> ' . date('M j, Y g:i A', strtotime($user_bid_status->bid_time)) . '</p>';
                echo '</div>';
                echo '<p>You can submit a new bid below.</p>';
                echo '</div>';
                echo '</div>';

                if ($is_auction_active) {
                    // Show regular bid form for new bid
                    echo '<h3>Place Your Bid</h3>';
                    echo '<form class="usf-bid-form" data-lot-id="' . esc_attr($lot->lot_id) . '">';
                    echo wp_nonce_field('usf_submit_bid', 'usf_bid_nonce', true, false);

                    echo '<div class="usf-form-group">';
                    echo '<label for="bid_amount">Bid Amount * (Minimum: $' . number_format($lot->min_offer, 2) . ')</label>';
                    echo '<input type="number" id="bid_amount" name="bid_amount" min="' . $lot->min_offer . '" step="0.01" required>';
                    echo '</div>';

                    echo '<div class="usf-form-group">';
                    echo '<label class="usf-checkbox-label">';
                    echo '<input type="checkbox" id="terms_accepted" name="terms_accepted" required>';
                    echo 'I agree to the terms and conditions *';
                    echo '</label>';
                    echo '</div>';

                    echo '<div class="usf-form-actions">';
                    echo '<button type="submit" class="usf-btn usf-btn-primary usf-btn-large">Submit Bid</button>';
                    echo '</div>';

                    echo '<div class="usf-bid-messages"></div>';
                    echo '</form>';
                }
            }
        } else {
            // User has no previous bid - show regular bid form
            if ($is_auction_active) {
                echo '<h3>Place Your Bid</h3>';
                echo '<form class="usf-bid-form" data-lot-id="' . esc_attr($lot->lot_id) . '">';
                echo wp_nonce_field('usf_submit_bid', 'usf_bid_nonce', true, false);

                echo '<div class="usf-form-group">';
                echo '<label for="bid_amount">Bid Amount * (Minimum: $' . number_format($lot->min_offer, 2) . ')</label>';
                echo '<input type="number" id="bid_amount" name="bid_amount" min="' . $lot->min_offer . '" step="0.01" required>';
                echo '</div>';

                echo '<div class="usf-form-group">';
                echo '<label class="usf-checkbox-label">';
                echo '<input type="checkbox" id="terms_accepted" name="terms_accepted" required>';
                echo 'I agree to the terms and conditions *';
                echo '</label>';
                echo '</div>';

                echo '<div class="usf-form-actions">';
                echo '<button type="submit" class="usf-btn usf-btn-primary usf-btn-large">Submit Bid</button>';
                echo '</div>';

                echo '<div class="usf-bid-messages"></div>';
                echo '</form>';
            }
        }

        echo '</div>';
    }

    /**
     * Render items table
     */
    private static function render_items_table($items) {
        echo '<div class="usf-items-table-container">';
        echo '<table class="usf-items-table">';
        echo '<thead>';
        echo '<tr>';
        echo '<th>Quantity</th>';
        echo '<th>Description</th>';
        echo '<th>Grade</th>';
        if (!empty($items[0]->capacity)) {
            echo '<th>Capacity</th>';
        }
        if (!empty($items[0]->color)) {
            echo '<th>Color</th>';
        }
        if (!empty($items[0]->carrier)) {
            echo '<th>Carrier</th>';
        }
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';

        foreach ($items as $item) {
            echo '<tr>';
            echo '<td>' . esc_html($item->quantity) . '</td>';
            echo '<td>' . esc_html($item->description) . '</td>';
            echo '<td>' . esc_html($item->grade) . '</td>';
            if (!empty($items[0]->capacity)) {
                echo '<td>' . esc_html($item->capacity) . '</td>';
            }
            if (!empty($items[0]->color)) {
                echo '<td>' . esc_html($item->color) . '</td>';
            }
            if (!empty($items[0]->carrier)) {
                echo '<td>' . esc_html($item->carrier) . '</td>';
            }
            echo '</tr>';
        }

        echo '</tbody>';
        echo '</table>';
        echo '</div>';
    }

    /**
     * Render pagination
     */
    private static function render_pagination($current_page, $total_items, $per_page) {
        $total_pages = ceil($total_items / $per_page);
        
        if ($total_pages <= 1) {
            return;
        }

        echo '<div class="usf-pagination">';
        
        // Previous page
        if ($current_page > 1) {
            $prev_url = add_query_arg('paged', $current_page - 1);
            echo '<a href="' . esc_url($prev_url) . '" class="usf-page-link usf-prev">« Previous</a>';
        }

        // Page numbers
        $start_page = max(1, $current_page - 2);
        $end_page = min($total_pages, $current_page + 2);

        if ($start_page > 1) {
            $first_url = add_query_arg('paged', 1);
            echo '<a href="' . esc_url($first_url) . '" class="usf-page-link">1</a>';
            if ($start_page > 2) {
                echo '<span class="usf-page-dots">...</span>';
            }
        }

        for ($i = $start_page; $i <= $end_page; $i++) {
            if ($i == $current_page) {
                echo '<span class="usf-page-link usf-current">' . $i . '</span>';
            } else {
                $page_url = add_query_arg('paged', $i);
                echo '<a href="' . esc_url($page_url) . '" class="usf-page-link">' . $i . '</a>';
            }
        }

        if ($end_page < $total_pages) {
            if ($end_page < $total_pages - 1) {
                echo '<span class="usf-page-dots">...</span>';
            }
            $last_url = add_query_arg('paged', $total_pages);
            echo '<a href="' . esc_url($last_url) . '" class="usf-page-link">' . $total_pages . '</a>';
        }

        // Next page
        if ($current_page < $total_pages) {
            $next_url = add_query_arg('paged', $current_page + 1);
            echo '<a href="' . esc_url($next_url) . '" class="usf-page-link usf-next">Next »</a>';
        }

        echo '</div>';
    }
}
