<?php

/**
 * Fired during plugin activation
 *
 * This class defines all code necessary to run during the plugin's activation.
 */

class USF_Activator {

    /**
     * Short Description. (use period)
     *
     * Long Description.
     */
    public static function activate() {
        
        // Create database tables
        require_once USF_AUCTION_PLUGIN_DIR . 'includes/class-usf-database.php';
        USF_Database::create_tables();

        // Set default options
        add_option('usf_auction_version', USF_AUCTION_VERSION);
        add_option('usf_auction_activated_time', current_time('mysql'));

        // Flush rewrite rules
        flush_rewrite_rules();

        // Schedule cron events
        if (!wp_next_scheduled('usf_check_auction_closings')) {
            wp_schedule_event(time(), 'hourly', 'usf_check_auction_closings');
        }
    }
}
