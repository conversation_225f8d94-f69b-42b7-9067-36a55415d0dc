<?php
/**
 * Admin Bids Management View
 *
 * @package USF_Auction
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get pending bids for display
global $wpdb;
$bids = $wpdb->get_results($wpdb->prepare("
    SELECT b.*, l.model, l.grade, l.auction_house
    FROM {$wpdb->prefix}auction_bids b
    LEFT JOIN {$wpdb->prefix}auction_lots l ON b.lot_id = l.lot_id
    ORDER BY b.bid_time DESC
    LIMIT %d
", 50));
?>

<div class="wrap">
    <h1>Manage Bids</h1>
    
    <div class="usf-bids-filters">
        <select id="bid-status-filter">
            <option value="">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="accepted">Accepted</option>
            <option value="rejected">Rejected</option>
        </select>
        <input type="text" id="bids-search" placeholder="Search by lot ID or bidder..." />
    </div>
    
    <div class="usf-bids-table-container">
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><input type="checkbox" id="select-all-bids"></th>
                    <th>Bid ID</th>
                    <th>Lot ID</th>
                    <th>Model</th>
                    <th>Bidder</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Bid Amount</th>
                    <th>Bid Time</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($bids)): ?>
                    <?php foreach ($bids as $bid): ?>
                        <tr>
                            <td><input type="checkbox" class="bid-checkbox" value="<?php echo esc_attr($bid->id); ?>"></td>
                            <td><?php echo esc_html($bid->id); ?></td>
                            <td><?php echo esc_html($bid->lot_id); ?></td>
                            <td><?php echo esc_html($bid->model); ?></td>
                            <td><?php echo esc_html($bid->user_name); ?></td>
                            <td><?php echo esc_html($bid->user_email); ?></td>
                            <td><?php echo esc_html($bid->user_phone); ?></td>
                            <td>$<?php echo number_format($bid->bid_amount, 2); ?></td>
                            <td><?php echo esc_html(date('M j, Y g:i A', strtotime($bid->bid_time))); ?></td>
                            <td>
                                <span class="usf-bid-status usf-bid-status-<?php echo esc_attr($bid->status); ?>">
                                    <?php echo esc_html(ucfirst($bid->status)); ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($bid->status === 'pending'): ?>
                                    <button class="button button-primary button-small" onclick="acceptBid(<?php echo esc_js($bid->id); ?>)">Accept</button>
                                    <button class="button button-small" onclick="rejectBid(<?php echo esc_js($bid->id); ?>)">Reject</button>
                                <?php else: ?>
                                    <button class="button button-small" onclick="viewBidDetails(<?php echo esc_js($bid->id); ?>)">View</button>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="11">No bids found.</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <div class="usf-bulk-actions">
        <select id="bulk-action-select">
            <option value="">Bulk Actions</option>
            <option value="accept">Accept Selected</option>
            <option value="reject">Reject Selected</option>
        </select>
        <button class="button" onclick="performBulkAction()">Apply</button>
    </div>
</div>

<!-- Bid Action Modal -->
<div id="bid-action-modal" class="usf-modal" style="display: none;">
    <div class="usf-modal-content">
        <span class="usf-modal-close">&times;</span>
        <h2 id="modal-title">Bid Action</h2>
        <form id="bid-action-form">
            <input type="hidden" id="action-bid-id" name="bid_id">
            <input type="hidden" id="action-type" name="action_type">
            
            <div class="usf-form-group">
                <label for="admin-notes">Admin Notes (optional):</label>
                <textarea id="admin-notes" name="admin_notes" rows="4" cols="50"></textarea>
            </div>
            
            <div class="usf-form-actions">
                <button type="submit" class="button button-primary">Confirm</button>
                <button type="button" class="button" onclick="closeModal()">Cancel</button>
            </div>
        </form>
    </div>
</div>

<style>
.usf-bids-filters {
    margin: 20px 0;
    display: flex;
    gap: 10px;
    align-items: center;
}

.usf-bids-filters input,
.usf-bids-filters select {
    padding: 5px 10px;
}

.usf-bids-table-container {
    margin-top: 20px;
}

.usf-bid-status {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.usf-bid-status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.usf-bid-status-accepted {
    background-color: #d4edda;
    color: #155724;
}

.usf-bid-status-rejected {
    background-color: #f8d7da;
    color: #721c24;
}

.usf-bulk-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    align-items: center;
}

.usf-modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.usf-modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 500px;
    border-radius: 4px;
}

.usf-modal-close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.usf-form-group {
    margin-bottom: 15px;
}

.usf-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.usf-form-actions {
    text-align: right;
}

.usf-form-actions .button {
    margin-left: 10px;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Form submission handler for bid actions
    $('#bid-action-form').on('submit', function(e) {
        e.preventDefault();
        
        const bidId = $('#action-bid-id').val();
        const actionType = $('#action-type').val();
        const adminNotes = $('#admin-notes').val();
        const submitButton = $(this).find('button[type="submit"]');
        
        // Disable submit button and show loading state
        submitButton.prop('disabled', true).text('Processing...');
        
        // Determine the AJAX action based on action type
        const ajaxAction = actionType === 'accept' ? 'usf_accept_bid' : 'usf_reject_bid';
        
        // Send AJAX request
        $.ajax({
            url: usf_admin_ajax.ajax_url,
            type: 'POST',
            data: {
                action: ajaxAction,
                bid_id: bidId,
                admin_notes: adminNotes,
                nonce: usf_admin_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    alert(response.data.message);
                    
                    // Close modal
                    closeModal();
                    
                    // Refresh the page to show updated status
                    location.reload();
                } else {
                    alert('Error: ' + response.data.message);
                }
            },
            error: function(xhr, status, error) {
                alert('An error occurred: ' + error);
            },
            complete: function() {
                // Re-enable submit button
                submitButton.prop('disabled', false).text('Confirm');
            }
        });
    });
    
    // Select all functionality
    $('#select-all-bids').on('change', function() {
        $('.bid-checkbox').prop('checked', this.checked);
    });
    
    // Close modal when clicking outside
    $(window).on('click', function(event) {
        if (event.target.id === 'bid-action-modal') {
            closeModal();
        }
    });
    
    // Close modal when clicking the X
    $('.usf-modal-close').on('click', function() {
        closeModal();
    });
});

function acceptBid(bidId) {
    showBidActionModal(bidId, 'accept', 'Accept Bid');
}

function rejectBid(bidId) {
    showBidActionModal(bidId, 'reject', 'Reject Bid');
}

function showBidActionModal(bidId, action, title) {
    document.getElementById('action-bid-id').value = bidId;
    document.getElementById('action-type').value = action;
    document.getElementById('modal-title').textContent = title;
    document.getElementById('bid-action-modal').style.display = 'block';
}

function closeModal() {
    document.getElementById('bid-action-modal').style.display = 'none';
    document.getElementById('admin-notes').value = '';
}

function viewBidDetails(bidId) {
    alert('View details for bid: ' + bidId);
}

function performBulkAction() {
    const action = document.getElementById('bulk-action-select').value;
    const selectedBids = Array.from(document.querySelectorAll('.bid-checkbox:checked')).map(cb => cb.value);
    
    if (!action || selectedBids.length === 0) {
        alert('Please select an action and at least one bid.');
        return;
    }
    
    if (confirm(`Are you sure you want to ${action} ${selectedBids.length} bid(s)?`)) {
        // Disable the apply button
        const applyButton = document.querySelector('.usf-bulk-actions .button');
        applyButton.disabled = true;
        applyButton.textContent = 'Processing...';
        
        // Send AJAX request for bulk action
        jQuery.ajax({
            url: usf_admin_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'usf_bulk_bid_action',
                action_type: action,
                bid_ids: selectedBids,
                admin_notes: '', // Could add a bulk notes field if needed
                nonce: usf_admin_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data.message);
                    location.reload();
                } else {
                    alert('Error: ' + response.data.message);
                }
            },
            error: function(xhr, status, error) {
                alert('An error occurred: ' + error);
            },
            complete: function() {
                applyButton.disabled = false;
                applyButton.textContent = 'Apply';
            }
        });
    }
}
</script>
